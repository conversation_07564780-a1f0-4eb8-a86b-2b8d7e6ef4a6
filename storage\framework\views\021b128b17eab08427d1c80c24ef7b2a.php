<?php $__env->startSection('title', 'Thống kê liên hệ'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary">
                    <i class="fas fa-envelope me-2"></i>Thống kê liên hệ
                </h2>
                <a href="<?php echo e(route('admin.thong-ke.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Quay lại tổng quan
                </a>
            </div>
        </div>
    </div>

    <!-- Bộ lọc -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('admin.thong-ke.lien-he')); ?>" class="row">
                        <div class="col-md-3 mb-2">
                            <label for="start_date" class="form-label">Từ ngày</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="<?php echo e(request('start_date')); ?>">
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="end_date" class="form-label">Đến ngày</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="<?php echo e(request('end_date')); ?>">
                        </div>
                        <div class="col-md-3 mb-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>Lọc
                                </button>
                                <a href="<?php echo e(route('admin.thong-ke.lien-he')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-refresh me-1"></i>Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê tổng hợp -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="h2 mb-0 fw-bold text-primary"><?php echo e(number_format($thongKeTheoTrangThai['chua_xu_ly'] + $thongKeTheoTrangThai['da_xu_ly'])); ?></div>
                    <div class="text-muted">Tổng liên hệ</div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="h2 mb-0 fw-bold text-warning"><?php echo e(number_format($thongKeTheoTrangThai['chua_xu_ly'])); ?></div>
                    <div class="text-muted">Chưa xử lý</div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="h2 mb-0 fw-bold text-success"><?php echo e(number_format($thongKeTheoTrangThai['da_xu_ly'])); ?></div>
                    <div class="text-muted">Đã xử lý</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Biểu đồ thống kê theo tháng -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0 fw-bold">Thống kê liên hệ theo tháng (6 tháng gần đây)</h5>
                </div>
                <div class="card-body">
                    <canvas id="chartTheoThang" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Danh sách liên hệ -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0 fw-bold">Danh sách liên hệ chi tiết</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>STT</th>
                                    <th>Thông tin liên hệ</th>
                                    <th>Nội dung</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày gửi</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $lienHes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $lienHe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($lienHes->firstItem() + $index); ?></td>
                                    <td>
                                        <div>
                                            <div class="fw-semibold"><?php echo e($lienHe->ten); ?></div>
                                            <div class="small text-muted">
                                                <i class="fas fa-envelope me-1"></i><?php echo e($lienHe->email); ?>

                                            </div>
                                            <?php if($lienHe->so_dien_thoai): ?>
                                            <div class="small text-muted">
                                                <i class="fas fa-phone me-1"></i><?php echo e($lienHe->so_dien_thoai); ?>

                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="small text-muted"><?php echo e(Str::limit($lienHe->noi_dung, 80)); ?></div>
                                    </td>
                                    <td>
                                        <?php if($lienHe->trang_thai == 'chua_xu_ly'): ?>
                                            <span class="badge bg-warning">Chưa xử lý</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">Đã xử lý</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($lienHe->create_at ? date('d/m/Y H:i', strtotime($lienHe->create_at)) : '---'); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.lien-he.show', $lienHe->id)); ?>" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if($lienHe->trang_thai == 'chua_xu_ly'): ?>
                                            <form action="<?php echo e(route('admin.lien-he.update-status', $lienHe->id)); ?>" method="POST" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <input type="hidden" name="trang_thai" value="da_xu_ly">
                                                <button type="submit" class="btn btn-sm btn-outline-success" title="Đánh dấu đã xử lý">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">Không có dữ liệu liên hệ</div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($lienHes->hasPages()): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($lienHes->appends(request()->query())->links()); ?>

                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Biểu đồ thống kê theo tháng
const ctxThang = document.getElementById('chartTheoThang').getContext('2d');
new Chart(ctxThang, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode(array_column($thongKeTheoThang, 'thang')); ?>,
        datasets: [{
            label: 'Số lượng liên hệ',
            data: <?php echo json_encode(array_column($thongKeTheoThang, 'so_luong')); ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/admin/thong-ke/lien-he.blade.php ENDPATH**/ ?>