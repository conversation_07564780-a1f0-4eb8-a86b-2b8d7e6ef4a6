<?php $__env->startSection('title', 'Thống kê phim'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary">
                    <i class="fas fa-film me-2"></i>Thống kê phim
                </h2>
                <a href="<?php echo e(route('admin.thong-ke.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Quay lại tổng quan
                </a>
            </div>
        </div>
    </div>

    <!-- Bộ lọc -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('admin.thong-ke.phim')); ?>" class="row">
                        <div class="col-md-3 mb-2">
                            <label for="start_date" class="form-label">Từ ngày</label>
                            <input type="date" name="start_date" id="start_date" class="form-control" value="<?php echo e(request('start_date')); ?>">
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="end_date" class="form-label">Đến ngày</label>
                            <input type="date" name="end_date" id="end_date" class="form-control" value="<?php echo e(request('end_date')); ?>">
                        </div>
                        <div class="col-md-3 mb-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>Lọc
                                </button>
                                <a href="<?php echo e(route('admin.thong-ke.phim')); ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-refresh me-1"></i>Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Thống kê tổng hợp -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="h2 mb-0 fw-bold text-primary"><?php echo e(number_format($thongKe['tong_phim'])); ?></div>
                    <div class="text-muted">Tổng số phim</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="h2 mb-0 fw-bold text-success"><?php echo e(number_format($thongKe['dang_chieu'])); ?></div>
                    <div class="text-muted">Đang chiếu</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="h2 mb-0 fw-bold text-warning"><?php echo e(number_format($thongKe['sap_chieu'])); ?></div>
                    <div class="text-muted">Sắp chiếu</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="h2 mb-0 fw-bold text-secondary"><?php echo e(number_format($thongKe['ngung_chieu'])); ?></div>
                    <div class="text-muted">Ngừng chiếu</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Danh sách phim -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0 fw-bold">Danh sách phim chi tiết</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>STT</th>
                                    <th>Tên phim</th>
                                    <th>Thể loại</th>
                                    <th>Trạng thái</th>
                                    <th>Số suất chiếu</th>
                                    <th>Ngày tạo</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $phims; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $phim): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($phims->firstItem() + $index); ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($phim->hinh_anh): ?>
                                            <img src="<?php echo e(asset('storage/' . $phim->hinh_anh)); ?>" alt="<?php echo e($phim->tieu_de); ?>" 
                                                 class="rounded me-2" style="width: 40px; height: 60px; object-fit: cover;">
                                            <?php endif; ?>
                                            <div>
                                                <div class="fw-semibold"><?php echo e($phim->tieu_de); ?></div>
                                                <div class="small text-muted"><?php echo e(Str::limit($phim->mo_ta, 50)); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($phim->theLoais && $phim->theLoais->count() > 0): ?>
                                            <?php $__currentLoopData = $phim->theLoais->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $theLoai): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="badge bg-light text-dark me-1"><?php echo e($theLoai->ten); ?></span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($phim->theLoais->count() > 2): ?>
                                                <span class="badge bg-secondary">+<?php echo e($phim->theLoais->count() - 2); ?></span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">Chưa phân loại</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                            $statusClass = match($phim->trang_thai) {
                                                'dang_chieu' => 'success',
                                                'sap_chieu' => 'warning',
                                                'ngung_chieu' => 'secondary',
                                                default => 'secondary'
                                            };
                                            $statusText = match($phim->trang_thai) {
                                                'dang_chieu' => 'Đang chiếu',
                                                'sap_chieu' => 'Sắp chiếu',
                                                'ngung_chieu' => 'Ngừng chiếu',
                                                default => ucfirst($phim->trang_thai)
                                            };
                                        ?>
                                        <span class="badge bg-<?php echo e($statusClass); ?>"><?php echo e($statusText); ?></span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info"><?php echo e($phim->suat_chieus_count ?? 0); ?></span>
                                    </td>
                                    <td><?php echo e($phim->created_at ? $phim->created_at->format('d/m/Y H:i') : '---'); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.phim.show', $phim->id)); ?>" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.phim.edit', $phim->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="text-muted">Không có dữ liệu phim</div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($phims->hasPages()): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($phims->appends(request()->query())->links()); ?>

                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/admin/thong-ke/phim.blade.php ENDPATH**/ ?>